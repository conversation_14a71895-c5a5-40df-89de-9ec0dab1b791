# Aplikasi Penghitungan Uang Makan Pegawai

## Deskripsi
Aplikasi Penghitungan Uang Makan Pegawai adalah aplikasi berbasis web yang dirancang dengan pendekatan mobile-first untuk mengelola dan menghitung uang makan pegawai berdasarkan golongan dan hari kerja. Aplikasi ini menggunakan penyimpanan lokal (localStorage) untuk menyimpan data dan dapat diakses melalui browser tanpa memerlukan koneksi internet.

## Fitur
- **Pengelolaan Data Pegawai**: Menambah, mengedit, dan menghapus data pegawai
- **Pengelolaan Golongan**: <PERSON><PERSON><PERSON>, mengedit, dan menghapus golongan dengan uang makan yang berbeda
- **Pengelolaan Hari Libur**: <PERSON><PERSON><PERSON>, menged<PERSON>, dan menghapus hari libur (selain <PERSON>u dan <PERSON>)
- **Perhitungan Otomatis**: Menghitung jumlah hari kerja dan total uang makan secara otomatis
- **Backup dan Restore**: Menyimpan dan memulihkan data dalam format JSON
- **Export Excel**: Mengekspor data dalam format Excel
- **Pengaturan**: Menyesuaikan informasi instansi dan pimpinan
- **Responsif**: Tampilan yang responsif untuk berbagai ukuran layar (mobile-first)

## Teknologi
- HTML5
- CSS3
- JavaScript (ES6+)
- Bootstrap 5
- SheetJS (untuk export Excel)
- LocalStorage API

## Cara Penggunaan

### Instalasi
1. Unduh atau clone repository ini
2. Buka file `index.html` di browser

### Penggunaan
1. **Dashboard**: Menampilkan ringkasan data pegawai, golongan, hari kerja, dan total uang makan
2. **Data Pegawai**: Mengelola data pegawai (nama, golongan, jumlah hari kerja)
3. **Golongan**: Mengelola data golongan (nama golongan, uang makan per hari)
4. **Hari Libur**: Mengelola data hari libur (tanggal, keterangan)
5. **Pengaturan**: Mengatur informasi instansi dan pimpinan

### Backup dan Restore
- Klik tombol "Backup Data" untuk menyimpan data dalam format JSON
- Klik tombol "Restore Data" untuk memulihkan data dari file JSON

### Export Excel
- Klik tombol "Export Excel" untuk mengekspor data dalam format Excel

## Perhitungan Hari Kerja
- Hari kerja dihitung secara otomatis (tidak termasuk Sabtu, Minggu, dan hari libur nasional)
- Hari libur dapat ditambahkan secara manual
- Jumlah hari kerja untuk setiap pegawai dapat disesuaikan

## Perhitungan Uang Makan
- Uang makan dihitung berdasarkan: Jumlah Hari Kerja × Uang Makan per Hari
- Uang makan per hari ditentukan berdasarkan golongan pegawai

## Catatan
- Data disimpan di localStorage browser, jadi akan tetap tersimpan meskipun browser ditutup
- Untuk keamanan data, lakukan backup secara berkala
- Aplikasi ini berjalan sepenuhnya di sisi klien (client-side), tidak memerlukan server