/**
 * Aplikasi Penghitungan Uang Makan Pegawai
 * Menggunakan localStorage untuk penyimpanan data
 * Fitur: Pengelolaan pegawai, golongan, hari libur, backup/restore, export excel
 */

// Inisialisasi data aplikasi
let appData = {
    pegawai: [],
    golongan: [],
    hariLibur: [],
    ketidakhadiran: [],
    pengaturan: {
        namaInstansi: '',
        alamatInstansi: '',
        namaPimpinan: '',
        nipPimpinan: ''
    }
};

// DOM Elements
const elements = {
    // Navigation
    navDashboard: document.getElementById('nav-dashboard'),
    navPegawai: document.getElementById('nav-pegawai'),
    navGolongan: document.getElementById('nav-golongan'),
    navHariLibur: document.getElementById('nav-hari-libur'),
    navKetidakhadiran: document.getElementById('nav-ketidakhadiran'),
    navPengaturan: document.getElementById('nav-pengaturan'),
    pageTitle: document.getElementById('page-title'),
    
    // Content Sections
    dashboardContent: document.getElementById('dashboard-content'),
    pegawaiContent: document.getElementById('pegawai-content'),
    golonganContent: document.getElementById('golongan-content'),
    hariLiburContent: document.getElementById('hari-libur-content'),
    ketidakhadiranContent: document.getElementById('ketidakhadiran-content'),
    pengaturanContent: document.getElementById('pengaturan-content'),
    
    // Dashboard Elements
    totalPegawai: document.getElementById('total-pegawai'),
    totalGolongan: document.getElementById('total-golongan'),
    totalHariKerja: document.getElementById('total-hari-kerja'),
    totalUangMakan: document.getElementById('total-uang-makan'),
    tableRingkasan: document.getElementById('table-ringkasan'),
    selectBulanDashboard: document.getElementById('select-bulan-dashboard'),
    selectTahunDashboard: document.getElementById('select-tahun-dashboard'),
    
    // Pegawai Elements
    tablePegawai: document.getElementById('table-pegawai'),
    btnTambahPegawai: document.getElementById('btn-tambah-pegawai'),
    modalPegawai: new bootstrap.Modal(document.getElementById('modal-pegawai')),
    formPegawai: document.getElementById('form-pegawai'),
    inputIdPegawai: document.getElementById('input-id-pegawai'),
    inputNamaPegawai: document.getElementById('input-nama-pegawai'),
    selectGolonganPegawai: document.getElementById('select-golongan-pegawai'),
    inputHariKerjaPegawai: document.getElementById('input-hari-kerja-pegawai'),
    btnSimpanPegawai: document.getElementById('btn-simpan-pegawai'),
    modalPegawaiLabel: document.getElementById('modal-pegawai-label'),
    
    // Golongan Elements
    tableGolongan: document.getElementById('table-golongan'),
    btnTambahGolongan: document.getElementById('btn-tambah-golongan'),
    modalGolongan: new bootstrap.Modal(document.getElementById('modal-golongan')),
    formGolongan: document.getElementById('form-golongan'),
    inputIdGolongan: document.getElementById('input-id-golongan'),
    inputNamaGolongan: document.getElementById('input-nama-golongan'),
    inputUangMakanGolongan: document.getElementById('input-uang-makan-golongan'),
    btnSimpanGolongan: document.getElementById('btn-simpan-golongan'),
    modalGolonganLabel: document.getElementById('modal-golongan-label'),
    
    // Hari Libur Elements
    tableHariLibur: document.getElementById('table-hari-libur'),
    btnTambahHariLibur: document.getElementById('btn-tambah-hari-libur'),
    modalHariLibur: new bootstrap.Modal(document.getElementById('modal-hari-libur')),
    formHariLibur: document.getElementById('form-hari-libur'),
    inputIdHariLibur: document.getElementById('input-id-hari-libur'),
    inputTanggalHariLibur: document.getElementById('input-tanggal-hari-libur'),
    inputKeteranganHariLibur: document.getElementById('input-keterangan-hari-libur'),
    btnSimpanHariLibur: document.getElementById('btn-simpan-hari-libur'),
    modalHariLiburLabel: document.getElementById('modal-hari-libur-label'),
    selectBulanHariLibur: document.getElementById('select-bulan-hari-libur'),
    selectTahunHariLibur: document.getElementById('select-tahun-hari-libur'),
    
    // Ketidakhadiran Elements
    tableKetidakhadiran: document.getElementById('table-ketidakhadiran'),
    btnTambahKetidakhadiran: document.getElementById('btn-tambah-ketidakhadiran'),
    modalKetidakhadiran: new bootstrap.Modal(document.getElementById('modal-ketidakhadiran')),
    formKetidakhadiran: document.getElementById('form-ketidakhadiran'),
    inputIdKetidakhadiran: document.getElementById('input-id-ketidakhadiran'),
    selectPegawaiKetidakhadiran: document.getElementById('select-pegawai-ketidakhadiran'),
    inputTanggalKetidakhadiran: document.getElementById('input-tanggal-ketidakhadiran'),
    inputKeteranganKetidakhadiran: document.getElementById('input-keterangan-ketidakhadiran'),
    btnSimpanKetidakhadiran: document.getElementById('btn-simpan-ketidakhadiran'),
    modalKetidakhadiranLabel: document.getElementById('modal-ketidakhadiran-label'),
    selectBulanKetidakhadiran: document.getElementById('select-bulan-ketidakhadiran'),
    selectTahunKetidakhadiran: document.getElementById('select-tahun-ketidakhadiran'),
    
    // Pengaturan Elements
    formPengaturan: document.getElementById('form-pengaturan'),
    inputNamaInstansi: document.getElementById('input-nama-instansi'),
    inputAlamatInstansi: document.getElementById('input-alamat-instansi'),
    inputNamaPimpinan: document.getElementById('input-nama-pimpinan'),
    inputNipPimpinan: document.getElementById('input-nip-pimpinan'),
    
    // Database Actions
    btnBackup: document.getElementById('btn-backup'),
    btnRestore: document.getElementById('btn-restore'),
    btnReset: document.getElementById('btn-reset'),
    inputRestoreFile: document.getElementById('input-restore-file'),
    modalKonfirmasiReset: new bootstrap.Modal(document.getElementById('modal-konfirmasi-reset')),
    btnKonfirmasiReset: document.getElementById('btn-konfirmasi-reset'),
    
    // Export
    btnExportExcel: document.getElementById('btn-export-excel')
};

// Inisialisasi aplikasi
document.addEventListener('DOMContentLoaded', () => {
    // Load data dari localStorage
    loadDataFromLocalStorage();
    
    // Inisialisasi event listeners
    initEventListeners();
    
    // Inisialisasi tahun untuk dropdown hari libur dan dashboard
    initTahunDropdown();
    initDashboardDropdowns();
    
    // Tampilkan dashboard sebagai halaman awal
    showDashboard();
    
    // Jika belum ada data golongan, tambahkan data default
    if (appData.golongan.length === 0) {
        addDefaultGolongan();
    }
});

// Fungsi untuk memuat data dari localStorage
function loadDataFromLocalStorage() {
    const savedData = localStorage.getItem('appUangMakan');
    if (savedData) {
        appData = JSON.parse(savedData);
    }
}

// Fungsi untuk menyimpan data ke localStorage
function saveDataToLocalStorage() {
    localStorage.setItem('appUangMakan', JSON.stringify(appData));
}

// Fungsi untuk menambahkan data golongan default
function addDefaultGolongan() {
    const defaultGolongan = [
        { id: generateId(), nama: 'Golongan 5', uangMakan: 50000 },
        { id: generateId(), nama: 'Golongan 8', uangMakan: 80000 }
    ];
    
    appData.golongan = defaultGolongan;
    saveDataToLocalStorage();
}

// Inisialisasi event listeners
function initEventListeners() {
    // Navigation
    elements.navDashboard.addEventListener('click', function() {
        showDashboard();
        toggleSidebarOnMobile();
    });
    elements.navPegawai.addEventListener('click', function() {
        showPegawai();
        toggleSidebarOnMobile();
    });
    elements.navGolongan.addEventListener('click', function() {
        showGolongan();
        toggleSidebarOnMobile();
    });
    elements.navHariLibur.addEventListener('click', function() {
        showHariLibur();
        toggleSidebarOnMobile();
    });
    elements.navKetidakhadiran.addEventListener('click', function() {
        showKetidakhadiran();
        toggleSidebarOnMobile();
    });
    elements.navPengaturan.addEventListener('click', function() {
        showPengaturan();
        toggleSidebarOnMobile();
    });
    
    // Sidebar Toggle untuk Mobile
    document.getElementById('sidebar-toggle').addEventListener('click', toggleSidebar);
    
    // Dashboard Bulan dan Tahun Dropdown/Input
    elements.selectBulanDashboard.addEventListener('change', function() {
        updateDashboardData();
        renderRingkasanTable();
    });
    elements.selectTahunDashboard.addEventListener('input', function() {
        // Validasi input tahun
        let tahun = parseInt(this.value);
        if (tahun < 2000) tahun = 2000;
        if (tahun > 2100) tahun = 2100;
        if (this.value !== tahun.toString()) this.value = tahun;
        
        updateDashboardData();
        renderRingkasanTable();
    });
    
    // Pegawai
    elements.btnTambahPegawai.addEventListener('click', showAddPegawaiModal);
    elements.btnSimpanPegawai.addEventListener('click', savePegawai);
    
    // Golongan
    elements.btnTambahGolongan.addEventListener('click', showAddGolonganModal);
    elements.btnSimpanGolongan.addEventListener('click', saveGolongan);
    
    // Hari Libur
    elements.btnTambahHariLibur.addEventListener('click', showAddHariLiburModal);
    elements.btnSimpanHariLibur.addEventListener('click', saveHariLibur);
    elements.selectBulanHariLibur.addEventListener('change', renderHariLiburTable);
    elements.selectTahunHariLibur.addEventListener('change', renderHariLiburTable);
    
    // Ketidakhadiran
    elements.btnTambahKetidakhadiran.addEventListener('click', showAddKetidakhadiranModal);
    elements.btnSimpanKetidakhadiran.addEventListener('click', saveKetidakhadiran);
    elements.selectBulanKetidakhadiran.addEventListener('change', renderKetidakhadiranTable);
    elements.selectTahunKetidakhadiran.addEventListener('input', renderKetidakhadiranTable);
    
    // Pengaturan
    elements.formPengaturan.addEventListener('submit', savePengaturan);
    
    // Database Actions
    elements.btnBackup.addEventListener('click', backupData);
    elements.btnRestore.addEventListener('click', () => elements.inputRestoreFile.click());
    elements.inputRestoreFile.addEventListener('change', restoreData);
    elements.btnReset.addEventListener('click', () => elements.modalKonfirmasiReset.show());
    elements.btnKonfirmasiReset.addEventListener('click', resetData);
    
    // Export
    elements.btnExportExcel.addEventListener('click', exportToExcel);
}

// Inisialisasi dropdown tahun untuk hari libur
function initTahunDropdown() {
    const currentYear = new Date().getFullYear();
    const selectTahun = elements.selectTahunHariLibur;
    
    // Kosongkan dropdown
    selectTahun.innerHTML = '';
    
    // Tambahkan opsi tahun (tahun sekarang - 1, tahun sekarang, tahun sekarang + 1)
    for (let year = currentYear - 1; year <= currentYear + 1; year++) {
        const option = document.createElement('option');
        option.value = year;
        option.textContent = year;
        selectTahun.appendChild(option);
    }
    
    // Set tahun sekarang sebagai default
    selectTahun.value = currentYear;
    
    // Set bulan sekarang sebagai default
    elements.selectBulanHariLibur.value = new Date().getMonth();
}

// Inisialisasi dropdown bulan dan input tahun untuk dashboard
function initDashboardDropdowns() {
    const currentYear = new Date().getFullYear();
    const currentMonth = new Date().getMonth();
    
    // Set tahun dan bulan sekarang sebagai default
    elements.selectTahunDashboard.value = currentYear;
    elements.selectBulanDashboard.value = currentMonth;
}

// Fungsi untuk menampilkan dashboard
function showDashboard() {
    // Update navigation
    updateNavigation('nav-dashboard', 'Dashboard');
    
    // Update dashboard data
    updateDashboardData();
    
    // Render ringkasan table
    renderRingkasanTable();
}

// Fungsi untuk menampilkan halaman pegawai
function showPegawai() {
    // Update navigation
    updateNavigation('nav-pegawai', 'Data Pegawai');
    
    // Render pegawai table
    renderPegawaiTable();
}

// Fungsi untuk menampilkan halaman golongan
function showGolongan() {
    // Update navigation
    updateNavigation('nav-golongan', 'Data Golongan');
    
    // Render golongan table
    renderGolonganTable();
}

// Fungsi untuk menampilkan halaman hari libur
function showHariLibur() {
    // Update navigation
    updateNavigation('nav-hari-libur', 'Data Hari Libur');
    
    // Render hari libur table
    renderHariLiburTable();
}

// Fungsi untuk menampilkan halaman ketidakhadiran
function showKetidakhadiran() {
    // Update navigation
    updateNavigation('nav-ketidakhadiran', 'Ketidakhadiran');
    
    // Inisialisasi tahun untuk dropdown ketidakhadiran jika belum diatur
    if (!elements.selectTahunKetidakhadiran.value) {
        elements.selectTahunKetidakhadiran.value = new Date().getFullYear();
    }
    
    // Set bulan sekarang sebagai default jika belum dipilih
    if (!elements.selectBulanKetidakhadiran.value) {
        elements.selectBulanKetidakhadiran.value = new Date().getMonth();
    }
    
    // Render tabel ketidakhadiran
    renderKetidakhadiranTable();
}

// Fungsi untuk render tabel ketidakhadiran
function renderKetidakhadiranTable() {
    const tbody = elements.tableKetidakhadiran.querySelector('tbody');
    tbody.innerHTML = '';
    
    const selectedMonth = parseInt(elements.selectBulanKetidakhadiran.value);
    const selectedYear = parseInt(elements.selectTahunKetidakhadiran.value);
    
    // Filter ketidakhadiran berdasarkan bulan dan tahun yang dipilih
    const filteredKetidakhadiran = appData.ketidakhadiran.filter(k => {
        const tanggal = new Date(k.tanggal);
        return tanggal.getMonth() === selectedMonth && tanggal.getFullYear() === selectedYear;
    });
    
    if (filteredKetidakhadiran.length === 0) {
        const tr = document.createElement('tr');
        tr.innerHTML = '<td colspan="5" class="text-center">Belum ada data ketidakhadiran</td>';
        tbody.appendChild(tr);
        return;
    }
    
    filteredKetidakhadiran.forEach((ketidakhadiran, index) => {
        const pegawai = appData.pegawai.find(p => p.id === ketidakhadiran.pegawaiId);
        if (!pegawai) return;
        
        const tanggal = new Date(ketidakhadiran.tanggal);
        const formattedTanggal = tanggal.toLocaleDateString('id-ID', { day: 'numeric', month: 'long', year: 'numeric' });
        
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${index + 1}</td>
            <td>${pegawai.nama}</td>
            <td>${formattedTanggal}</td>
            <td>${ketidakhadiran.keterangan}</td>
            <td>
                <button class="btn btn-sm btn-outline-primary me-1" onclick="showEditKetidakhadiranModal('${ketidakhadiran.id}')">
                    <i class="bi bi-pencil"></i>
                </button>
                <button class="btn btn-sm btn-outline-danger" onclick="deleteKetidakhadiran('${ketidakhadiran.id}')">
                    <i class="bi bi-trash"></i>
                </button>
            </td>
        `;
        tbody.appendChild(tr);
    });
}

// Fungsi untuk menampilkan halaman pengaturan
function showPengaturan() {
    // Update navigation
    updateNavigation('nav-pengaturan', 'Pengaturan');
    
    // Render pengaturan form
    renderPengaturanForm();
}

// Fungsi untuk update navigasi
function updateNavigation(activeNavId, title) {
    // Hide all content sections
    elements.dashboardContent.classList.add('d-none');
    elements.pegawaiContent.classList.add('d-none');
    elements.golonganContent.classList.add('d-none');
    elements.hariLiburContent.classList.add('d-none');
    elements.ketidakhadiranContent.classList.add('d-none');
    elements.pengaturanContent.classList.add('d-none');
    
    // Remove active class from all nav links
    elements.navDashboard.classList.remove('active');
    elements.navPegawai.classList.remove('active');
    elements.navGolongan.classList.remove('active');
    elements.navHariLibur.classList.remove('active');
    elements.navKetidakhadiran.classList.remove('active');
    elements.navPengaturan.classList.remove('active');
    
    // Add active class to selected nav link
    document.getElementById(activeNavId).classList.add('active');
    
    // Show selected content section
    switch (activeNavId) {
        case 'nav-dashboard':
            elements.dashboardContent.classList.remove('d-none');
            break;
        case 'nav-pegawai':
            elements.pegawaiContent.classList.remove('d-none');
            break;
        case 'nav-golongan':
            elements.golonganContent.classList.remove('d-none');
            break;
        case 'nav-hari-libur':
            elements.hariLiburContent.classList.remove('d-none');
            break;
        case 'nav-ketidakhadiran':
            elements.ketidakhadiranContent.classList.remove('d-none');
            break;
        case 'nav-pengaturan':
            elements.pengaturanContent.classList.remove('d-none');
            break;
    }
    
    // Update page title
    elements.pageTitle.textContent = title;
}

// Fungsi untuk update data dashboard
function updateDashboardData() {
    // Update total pegawai
    elements.totalPegawai.textContent = appData.pegawai.length;
    
    // Update total golongan
    elements.totalGolongan.textContent = appData.golongan.length;
    
    // Ambil bulan dan tahun yang dipilih
    const selectedMonth = parseInt(elements.selectBulanDashboard.value);
    let selectedYear = parseInt(elements.selectTahunDashboard.value);
    
    // Validasi tahun jika input kosong atau tidak valid
    if (isNaN(selectedYear) || selectedYear < 2000 || selectedYear > 2100) {
        selectedYear = new Date().getFullYear();
        elements.selectTahunDashboard.value = selectedYear;
    }
    
    // Update total hari kerja untuk bulan dan tahun yang dipilih
    const hariKerja = hitungHariKerja(selectedMonth, selectedYear, null);
    elements.totalHariKerja.textContent = hariKerja;
    
    // Update total uang makan berdasarkan bulan dan tahun yang dipilih
    const totalUangMakan = hitungTotalUangMakan(selectedMonth, selectedYear);
    elements.totalUangMakan.textContent = formatRupiah(totalUangMakan);
}

// Fungsi untuk render tabel ringkasan
function renderRingkasanTable() {
    const tbody = elements.tableRingkasan.querySelector('tbody');
    tbody.innerHTML = '';
    
    // Ambil bulan dan tahun yang dipilih
    const selectedMonth = parseInt(elements.selectBulanDashboard.value);
    let selectedYear = parseInt(elements.selectTahunDashboard.value);
    
    // Validasi tahun jika input kosong atau tidak valid
    if (isNaN(selectedYear) || selectedYear < 2000 || selectedYear > 2100) {
        selectedYear = new Date().getFullYear();
        elements.selectTahunDashboard.value = selectedYear;
    }
    
    // Tampilkan bulan dan tahun yang dipilih di judul tabel
    const bulanNames = ['Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni', 'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'];
    const bulanTahunText = `${bulanNames[selectedMonth]} ${selectedYear}`;
    document.querySelector('.card-header .card-title').textContent = `Ringkasan Uang Makan - ${bulanTahunText}`;
    
    if (appData.pegawai.length === 0) {
        const tr = document.createElement('tr');
        tr.innerHTML = '<td colspan="6" class="text-center">Belum ada data pegawai</td>';
        tbody.appendChild(tr);
        return;
    }
    
    appData.pegawai.forEach((pegawai, index) => {
        const golongan = appData.golongan.find(g => g.id === pegawai.golonganId);
        if (!golongan) return;
        
        // Jika pegawai memiliki hari kerja kustom, gunakan itu
        // Jika tidak, hitung hari kerja berdasarkan bulan dan tahun yang dipilih, dengan memperhitungkan ketidakhadiran
        const hariKerja = pegawai.hariKerja || hitungHariKerja(selectedMonth, selectedYear, pegawai.id);
        const uangMakanPerHari = golongan.uangMakan;
        const totalUangMakan = hariKerja * uangMakanPerHari;
        
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${index + 1}</td>
            <td>${pegawai.nama}</td>
            <td>${golongan.nama}</td>
            <td>${hariKerja}</td>
            <td>${formatRupiah(uangMakanPerHari)}</td>
            <td>${formatRupiah(totalUangMakan)}</td>
        `;
        
        tbody.appendChild(tr);
    });
}

// Fungsi untuk render tabel pegawai
function renderPegawaiTable() {
    const tbody = elements.tablePegawai.querySelector('tbody');
    tbody.innerHTML = '';
    
    if (appData.pegawai.length === 0) {
        const tr = document.createElement('tr');
        tr.innerHTML = '<td colspan="5" class="text-center">Belum ada data pegawai</td>';
        tbody.appendChild(tr);
        return;
    }
    
    appData.pegawai.forEach((pegawai, index) => {
        const golongan = appData.golongan.find(g => g.id === pegawai.golonganId);
        if (!golongan) return;
        
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${index + 1}</td>
            <td>${pegawai.nama}</td>
            <td>${golongan.nama}</td>
            <td>${pegawai.hariKerja || hitungHariKerja(null, null, pegawai.id)}</td>
            <td>
                <button class="btn btn-sm btn-primary me-1 btn-edit-pegawai" data-id="${pegawai.id}">
                    <i class="bi bi-pencil-square"></i>
                </button>
                <button class="btn btn-sm btn-danger btn-delete-pegawai" data-id="${pegawai.id}">
                    <i class="bi bi-trash"></i>
                </button>
            </td>
        `;
        
        tbody.appendChild(tr);
    });
    
    // Add event listeners for edit and delete buttons
    document.querySelectorAll('.btn-edit-pegawai').forEach(btn => {
        btn.addEventListener('click', () => {
            const pegawaiId = btn.getAttribute('data-id');
            showEditPegawaiModal(pegawaiId);
        });
    });
    
    document.querySelectorAll('.btn-delete-pegawai').forEach(btn => {
        btn.addEventListener('click', () => {
            const pegawaiId = btn.getAttribute('data-id');
            deletePegawai(pegawaiId);
        });
    });
}

// Fungsi untuk render tabel golongan
function renderGolonganTable() {
    const tbody = elements.tableGolongan.querySelector('tbody');
    tbody.innerHTML = '';
    
    if (appData.golongan.length === 0) {
        const tr = document.createElement('tr');
        tr.innerHTML = '<td colspan="4" class="text-center">Belum ada data golongan</td>';
        tbody.appendChild(tr);
        return;
    }
    
    appData.golongan.forEach((golongan, index) => {
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${index + 1}</td>
            <td>${golongan.nama}</td>
            <td>${formatRupiah(golongan.uangMakan)}</td>
            <td>
                <button class="btn btn-sm btn-primary me-1 btn-edit-golongan" data-id="${golongan.id}">
                    <i class="bi bi-pencil-square"></i>
                </button>
                <button class="btn btn-sm btn-danger btn-delete-golongan" data-id="${golongan.id}">
                    <i class="bi bi-trash"></i>
                </button>
            </td>
        `;
        
        tbody.appendChild(tr);
    });
    
    // Add event listeners for edit and delete buttons
    document.querySelectorAll('.btn-edit-golongan').forEach(btn => {
        btn.addEventListener('click', () => {
            const golonganId = btn.getAttribute('data-id');
            showEditGolonganModal(golonganId);
        });
    });
    
    document.querySelectorAll('.btn-delete-golongan').forEach(btn => {
        btn.addEventListener('click', () => {
            const golonganId = btn.getAttribute('data-id');
            deleteGolongan(golonganId);
        });
    });
}

// Fungsi untuk render tabel hari libur
function renderHariLiburTable() {
    const tbody = elements.tableHariLibur.querySelector('tbody');
    tbody.innerHTML = '';
    
    const selectedMonth = parseInt(elements.selectBulanHariLibur.value);
    const selectedYear = parseInt(elements.selectTahunHariLibur.value);
    
    // Filter hari libur berdasarkan bulan dan tahun yang dipilih
    const filteredHariLibur = appData.hariLibur.filter(hl => {
        const tanggal = new Date(hl.tanggal);
        return tanggal.getMonth() === selectedMonth && tanggal.getFullYear() === selectedYear;
    });
    
    if (filteredHariLibur.length === 0) {
        const tr = document.createElement('tr');
        tr.innerHTML = '<td colspan="4" class="text-center">Belum ada data hari libur untuk bulan dan tahun yang dipilih</td>';
        tbody.appendChild(tr);
        return;
    }
    
    filteredHariLibur.forEach((hariLibur, index) => {
        const tanggal = new Date(hariLibur.tanggal);
        const formattedTanggal = tanggal.toLocaleDateString('id-ID', { weekday: 'long', day: 'numeric', month: 'long', year: 'numeric' });
        
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${index + 1}</td>
            <td>${formattedTanggal}</td>
            <td>${hariLibur.keterangan}</td>
            <td>
                <button class="btn btn-sm btn-primary me-1 btn-edit-hari-libur" data-id="${hariLibur.id}">
                    <i class="bi bi-pencil-square"></i>
                </button>
                <button class="btn btn-sm btn-danger btn-delete-hari-libur" data-id="${hariLibur.id}">
                    <i class="bi bi-trash"></i>
                </button>
            </td>
        `;
        
        tbody.appendChild(tr);
    });
    
    // Add event listeners for edit and delete buttons
    document.querySelectorAll('.btn-edit-hari-libur').forEach(btn => {
        btn.addEventListener('click', () => {
            const hariLiburId = btn.getAttribute('data-id');
            showEditHariLiburModal(hariLiburId);
        });
    });
    
    document.querySelectorAll('.btn-delete-hari-libur').forEach(btn => {
        btn.addEventListener('click', () => {
            const hariLiburId = btn.getAttribute('data-id');
            deleteHariLibur(hariLiburId);
        });
    });
}

// Fungsi untuk render form pengaturan
function renderPengaturanForm() {
    elements.inputNamaInstansi.value = appData.pengaturan.namaInstansi || '';
    elements.inputAlamatInstansi.value = appData.pengaturan.alamatInstansi || '';
    elements.inputNamaPimpinan.value = appData.pengaturan.namaPimpinan || '';
    elements.inputNipPimpinan.value = appData.pengaturan.nipPimpinan || '';
}

// Fungsi untuk menampilkan modal tambah pegawai
function showAddPegawaiModal() {
    // Reset form
    elements.formPegawai.reset();
    elements.inputIdPegawai.value = '';
    elements.modalPegawaiLabel.textContent = 'Tambah Pegawai';
    
    // Populate golongan dropdown
    populateGolonganDropdown();
    
    // Set default hari kerja
    elements.inputHariKerjaPegawai.value = hitungHariKerja(null, null, null);
    
    // Show modal
    elements.modalPegawai.show();
}

// Fungsi untuk menampilkan modal edit pegawai
function showEditPegawaiModal(pegawaiId) {
    const pegawai = appData.pegawai.find(p => p.id === pegawaiId);
    if (!pegawai) return;
    
    // Reset form
    elements.formPegawai.reset();
    elements.inputIdPegawai.value = pegawai.id;
    elements.modalPegawaiLabel.textContent = 'Edit Pegawai';
    
    // Populate golongan dropdown
    populateGolonganDropdown();
    
    // Set form values
    elements.inputNamaPegawai.value = pegawai.nama;
    elements.selectGolonganPegawai.value = pegawai.golonganId;
    elements.inputHariKerjaPegawai.value = pegawai.hariKerja || hitungHariKerja(null, null, pegawai.id);
    
    // Show modal
    elements.modalPegawai.show();
}

// Fungsi untuk menampilkan modal tambah golongan
function showAddGolonganModal() {
    // Reset form
    elements.formGolongan.reset();
    elements.inputIdGolongan.value = '';
    elements.modalGolonganLabel.textContent = 'Tambah Golongan';
    
    // Show modal
    elements.modalGolongan.show();
}

// Fungsi untuk menampilkan modal edit golongan
function showEditGolonganModal(golonganId) {
    const golongan = appData.golongan.find(g => g.id === golonganId);
    if (!golongan) return;
    
    // Reset form
    elements.formGolongan.reset();
    elements.inputIdGolongan.value = golongan.id;
    elements.modalGolonganLabel.textContent = 'Edit Golongan';
    
    // Set form values
    elements.inputNamaGolongan.value = golongan.nama;
    elements.inputUangMakanGolongan.value = golongan.uangMakan;
    
    // Show modal
    elements.modalGolongan.show();
}

// Fungsi untuk menampilkan modal tambah hari libur
function showAddHariLiburModal() {
    // Reset form
    elements.formHariLibur.reset();
    elements.inputIdHariLibur.value = '';
    elements.modalHariLiburLabel.textContent = 'Tambah Hari Libur';
    
    // Set default tanggal (hari ini)
    const today = new Date();
    const formattedDate = today.toISOString().split('T')[0];
    elements.inputTanggalHariLibur.value = formattedDate;
    
    // Show modal
    elements.modalHariLibur.show();
}

// Fungsi untuk menampilkan modal edit hari libur
function showEditHariLiburModal(hariLiburId) {
    const hariLibur = appData.hariLibur.find(hl => hl.id === hariLiburId);
    if (!hariLibur) return;
    
    // Reset form
    elements.formHariLibur.reset();
    elements.inputIdHariLibur.value = hariLibur.id;
    elements.modalHariLiburLabel.textContent = 'Edit Hari Libur';
    
    // Set form values
    const tanggal = new Date(hariLibur.tanggal);
    const formattedDate = tanggal.toISOString().split('T')[0];
    elements.inputTanggalHariLibur.value = formattedDate;
    elements.inputKeteranganHariLibur.value = hariLibur.keterangan;
    
    // Show modal
    elements.modalHariLibur.show();
}

// Fungsi untuk menampilkan modal tambah ketidakhadiran
function showAddKetidakhadiranModal() {
    // Reset form
    elements.formKetidakhadiran.reset();
    elements.inputIdKetidakhadiran.value = '';
    elements.modalKetidakhadiranLabel.textContent = 'Tambah Ketidakhadiran';
    
    // Populate pegawai dropdown
    populatePegawaiDropdown();
    
    // Set tanggal default ke hari ini
    const today = new Date();
    const formattedToday = today.toISOString().split('T')[0];
    elements.inputTanggalKetidakhadiran.value = formattedToday;
    
    // Show modal
    elements.modalKetidakhadiran.show();
}

// Fungsi untuk menampilkan modal edit ketidakhadiran
function showEditKetidakhadiranModal(ketidakhadiranId) {
    const ketidakhadiran = appData.ketidakhadiran.find(k => k.id === ketidakhadiranId);
    if (!ketidakhadiran) return;
    
    // Reset form
    elements.formKetidakhadiran.reset();
    elements.inputIdKetidakhadiran.value = ketidakhadiran.id;
    elements.modalKetidakhadiranLabel.textContent = 'Edit Ketidakhadiran';
    
    // Populate pegawai dropdown
    populatePegawaiDropdown();
    
    // Set form values
    elements.selectPegawaiKetidakhadiran.value = ketidakhadiran.pegawaiId;
    const tanggal = new Date(ketidakhadiran.tanggal);
    const formattedTanggal = tanggal.toISOString().split('T')[0];
    elements.inputTanggalKetidakhadiran.value = formattedTanggal;
    elements.inputKeteranganKetidakhadiran.value = ketidakhadiran.keterangan;
    
    // Show modal
    elements.modalKetidakhadiran.show();
}

// Fungsi untuk mengisi dropdown golongan
function populateGolonganDropdown() {
    const select = elements.selectGolonganPegawai;
    select.innerHTML = '';
    
    // Add default option
    const defaultOption = document.createElement('option');
    defaultOption.value = '';
    defaultOption.textContent = '-- Pilih Golongan --';
    select.appendChild(defaultOption);
    
    // Add golongan options
    appData.golongan.forEach(golongan => {
        const option = document.createElement('option');
        option.value = golongan.id;
        option.textContent = golongan.nama;
        select.appendChild(option);
    });
}

// Fungsi untuk mengisi dropdown pegawai
function populatePegawaiDropdown() {
    const select = elements.selectPegawaiKetidakhadiran;
    select.innerHTML = '<option value="" disabled selected>Pilih Pegawai</option>';
    
    appData.pegawai.forEach(pegawai => {
        const option = document.createElement('option');
        option.value = pegawai.id;
        option.textContent = pegawai.nama;
        select.appendChild(option);
    });
}

// Fungsi untuk menyimpan data ketidakhadiran
function saveKetidakhadiran() {
    // Validate form
    if (!elements.formKetidakhadiran.checkValidity()) {
        elements.formKetidakhadiran.reportValidity();
        return;
    }
    
    const ketidakhadiranId = elements.inputIdKetidakhadiran.value;
    const pegawaiId = elements.selectPegawaiKetidakhadiran.value;
    const tanggal = elements.inputTanggalKetidakhadiran.value;
    const keterangan = elements.inputKeteranganKetidakhadiran.value;
    
    if (ketidakhadiranId) {
        // Update existing ketidakhadiran
        const index = appData.ketidakhadiran.findIndex(k => k.id === ketidakhadiranId);
        if (index !== -1) {
            appData.ketidakhadiran[index] = {
                ...appData.ketidakhadiran[index],
                pegawaiId,
                tanggal,
                keterangan
            };
        }
    } else {
        // Add new ketidakhadiran
        const newKetidakhadiran = {
            id: generateId(),
            pegawaiId,
            tanggal,
            keterangan
        };
        
        appData.ketidakhadiran.push(newKetidakhadiran);
    }
    
    // Save data to localStorage
    saveDataToLocalStorage();
    
    // Hide modal
    elements.modalKetidakhadiran.hide();
    
    // Refresh ketidakhadiran table
    renderKetidakhadiranTable();
    
    // Update dashboard data if we're on the current month
    const selectedMonth = parseInt(elements.selectBulanKetidakhadiran.value);
    const selectedYear = parseInt(elements.selectTahunKetidakhadiran.value);
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();
    
    if (selectedMonth === currentMonth && selectedYear === currentYear) {
        updateDashboardData();
        renderRingkasanTable();
    }
    
    // Show toast notification
    showToast('Data ketidakhadiran berhasil disimpan');
}

// Fungsi untuk menghapus ketidakhadiran
function deleteKetidakhadiran(ketidakhadiranId) {
    if (!confirm('Apakah Anda yakin ingin menghapus data ketidakhadiran ini?')) return;
    
    // Remove ketidakhadiran from array
    appData.ketidakhadiran = appData.ketidakhadiran.filter(k => k.id !== ketidakhadiranId);
    
    // Save data to localStorage
    saveDataToLocalStorage();
    
    // Refresh ketidakhadiran table
    renderKetidakhadiranTable();
    
    // Update dashboard data if we're on the current month
    const selectedMonth = parseInt(elements.selectBulanKetidakhadiran.value);
    const selectedYear = parseInt(elements.selectTahunKetidakhadiran.value);
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();
    
    if (selectedMonth === currentMonth && selectedYear === currentYear) {
        updateDashboardData();
        renderRingkasanTable();
    }
    
    // Show toast notification
    showToast('Data ketidakhadiran berhasil dihapus');
}

// Fungsi untuk menyimpan data pegawai
function savePegawai() {
    // Validate form
    if (!elements.formPegawai.checkValidity()) {
        elements.formPegawai.reportValidity();
        return;
    }
    
    const pegawaiId = elements.inputIdPegawai.value;
    const nama = elements.inputNamaPegawai.value;
    const golonganId = elements.selectGolonganPegawai.value;
    const hariKerja = parseInt(elements.inputHariKerjaPegawai.value);
    
    if (pegawaiId) {
        // Update existing pegawai
        const index = appData.pegawai.findIndex(p => p.id === pegawaiId);
        if (index !== -1) {
            appData.pegawai[index] = {
                ...appData.pegawai[index],
                nama,
                golonganId,
                hariKerja
            };
        }
    } else {
        // Add new pegawai
        const newPegawai = {
            id: generateId(),
            nama,
            golonganId,
            hariKerja
        };
        
        appData.pegawai.push(newPegawai);
    }
    
    // Save data to localStorage
    saveDataToLocalStorage();
    
    // Hide modal
    elements.modalPegawai.hide();
    
    // Refresh pegawai table
    renderPegawaiTable();
    
    // Update dashboard if visible
    if (!elements.dashboardContent.classList.contains('d-none')) {
        updateDashboardData();
        renderRingkasanTable();
    }
    
    // Show success message
    showToast('Pegawai berhasil disimpan');
}

// Fungsi untuk menyimpan data golongan
function saveGolongan() {
    // Validate form
    if (!elements.formGolongan.checkValidity()) {
        elements.formGolongan.reportValidity();
        return;
    }
    
    const golonganId = elements.inputIdGolongan.value;
    const nama = elements.inputNamaGolongan.value;
    const uangMakan = parseInt(elements.inputUangMakanGolongan.value);
    
    if (golonganId) {
        // Update existing golongan
        const index = appData.golongan.findIndex(g => g.id === golonganId);
        if (index !== -1) {
            appData.golongan[index] = {
                ...appData.golongan[index],
                nama,
                uangMakan
            };
        }
    } else {
        // Add new golongan
        const newGolongan = {
            id: generateId(),
            nama,
            uangMakan
        };
        
        appData.golongan.push(newGolongan);
    }
    
    // Save data to localStorage
    saveDataToLocalStorage();
    
    // Hide modal
    elements.modalGolongan.hide();
    
    // Refresh golongan table
    renderGolonganTable();
    
    // Update dashboard if visible
    if (!elements.dashboardContent.classList.contains('d-none')) {
        updateDashboardData();
        renderRingkasanTable();
    }
    
    // Show success message
    showToast('Golongan berhasil disimpan');
}

// Fungsi untuk menyimpan data hari libur
function saveHariLibur() {
    // Validate form
    if (!elements.formHariLibur.checkValidity()) {
        elements.formHariLibur.reportValidity();
        return;
    }
    
    const hariLiburId = elements.inputIdHariLibur.value;
    const tanggal = elements.inputTanggalHariLibur.value;
    const keterangan = elements.inputKeteranganHariLibur.value;
    
    if (hariLiburId) {
        // Update existing hari libur
        const index = appData.hariLibur.findIndex(hl => hl.id === hariLiburId);
        if (index !== -1) {
            appData.hariLibur[index] = {
                ...appData.hariLibur[index],
                tanggal,
                keterangan
            };
        }
    } else {
        // Add new hari libur
        const newHariLibur = {
            id: generateId(),
            tanggal,
            keterangan
        };
        
        appData.hariLibur.push(newHariLibur);
    }
    
    // Save data to localStorage
    saveDataToLocalStorage();
    
    // Hide modal
    elements.modalHariLibur.hide();
    
    // Refresh hari libur table
    renderHariLiburTable();
    
    // Update dashboard if visible
    if (!elements.dashboardContent.classList.contains('d-none')) {
        updateDashboardData();
        renderRingkasanTable();
    }
    
    // Show success message
    showToast('Hari libur berhasil disimpan');
}

// Fungsi untuk menyimpan pengaturan
function savePengaturan(event) {
    event.preventDefault();
    
    // Get form values
    const namaInstansi = elements.inputNamaInstansi.value;
    const alamatInstansi = elements.inputAlamatInstansi.value;
    const namaPimpinan = elements.inputNamaPimpinan.value;
    const nipPimpinan = elements.inputNipPimpinan.value;
    
    // Update pengaturan
    appData.pengaturan = {
        namaInstansi,
        alamatInstansi,
        namaPimpinan,
        nipPimpinan
    };
    
    // Save data to localStorage
    saveDataToLocalStorage();
    
    // Show success message
    showToast('Pengaturan berhasil disimpan');
}

// Fungsi untuk menghapus pegawai
function deletePegawai(pegawaiId) {
    if (confirm('Apakah Anda yakin ingin menghapus pegawai ini?')) {
        // Remove pegawai from array
        appData.pegawai = appData.pegawai.filter(p => p.id !== pegawaiId);
        
        // Save data to localStorage
        saveDataToLocalStorage();
        
        // Refresh pegawai table
        renderPegawaiTable();
        
        // Update dashboard if visible
        if (!elements.dashboardContent.classList.contains('d-none')) {
            updateDashboardData();
            renderRingkasanTable();
        }
        
        // Show success message
        showToast('Pegawai berhasil dihapus');
    }
}

// Fungsi untuk menghapus golongan
function deleteGolongan(golonganId) {
    // Check if golongan is used by any pegawai
    const isUsed = appData.pegawai.some(p => p.golonganId === golonganId);
    
    if (isUsed) {
        alert('Golongan ini tidak dapat dihapus karena sedang digunakan oleh pegawai.');
        return;
    }
    
    if (confirm('Apakah Anda yakin ingin menghapus golongan ini?')) {
        // Remove golongan from array
        appData.golongan = appData.golongan.filter(g => g.id !== golonganId);
        
        // Save data to localStorage
        saveDataToLocalStorage();
        
        // Refresh golongan table
        renderGolonganTable();
        
        // Update dashboard if visible
        if (!elements.dashboardContent.classList.contains('d-none')) {
            updateDashboardData();
        }
        
        // Show success message
        showToast('Golongan berhasil dihapus');
    }
}

// Fungsi untuk menghapus hari libur
function deleteHariLibur(hariLiburId) {
    if (confirm('Apakah Anda yakin ingin menghapus hari libur ini?')) {
        // Remove hari libur from array
        appData.hariLibur = appData.hariLibur.filter(hl => hl.id !== hariLiburId);
        
        // Save data to localStorage
        saveDataToLocalStorage();
        
        // Refresh hari libur table
        renderHariLiburTable();
        
        // Update dashboard if visible
        if (!elements.dashboardContent.classList.contains('d-none')) {
            updateDashboardData();
            renderRingkasanTable();
        }
        
        // Show success message
        showToast('Hari libur berhasil dihapus');
    }
}

// Fungsi untuk backup data
function backupData() {
    // Create a JSON string from appData
    const jsonData = JSON.stringify(appData, null, 2);
    
    // Create a Blob from the JSON string
    const blob = new Blob([jsonData], { type: 'application/json' });
    
    // Create a URL for the Blob
    const url = URL.createObjectURL(blob);
    
    // Create a temporary anchor element
    const a = document.createElement('a');
    a.href = url;
    a.download = `backup_uang_makan_${new Date().toISOString().split('T')[0]}.json`;
    
    // Trigger a click on the anchor element
    document.body.appendChild(a);
    a.click();
    
    // Clean up
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    // Show success message
    showToast('Backup data berhasil');
}

// Fungsi untuk restore data
function restoreData(event) {
    const file = event.target.files[0];
    if (!file) return;
    
    const reader = new FileReader();
    
    reader.onload = function(e) {
        try {
            const data = JSON.parse(e.target.result);
            
            // Validate data structure
            if (!data.pegawai || !data.golongan || !data.hariLibur || !data.pengaturan) {
                throw new Error('Format file backup tidak valid');
            }
            
            // Update appData
            appData = data;
            
            // Save data to localStorage
            saveDataToLocalStorage();
            
            // Refresh current view
            if (!elements.dashboardContent.classList.contains('d-none')) {
                updateDashboardData();
                renderRingkasanTable();
            } else if (!elements.pegawaiContent.classList.contains('d-none')) {
                renderPegawaiTable();
            } else if (!elements.golonganContent.classList.contains('d-none')) {
                renderGolonganTable();
            } else if (!elements.hariLiburContent.classList.contains('d-none')) {
                renderHariLiburTable();
            } else if (!elements.pengaturanContent.classList.contains('d-none')) {
                renderPengaturanForm();
            }
            
            // Show success message
            showToast('Restore data berhasil');
        } catch (error) {
            alert(`Error: ${error.message}`);
        }
    };
    
    reader.readAsText(file);
    
    // Reset file input
    event.target.value = '';
}

// Fungsi untuk reset data
function resetData() {
    // Reset appData
    appData = {
        pegawai: [],
        golongan: [],
        hariLibur: [],
        pengaturan: {
            namaInstansi: '',
            alamatInstansi: '',
            namaPimpinan: '',
            nipPimpinan: ''
        }
    };
    
    // Add default golongan
    addDefaultGolongan();
    
    // Save data to localStorage
    saveDataToLocalStorage();
    
    // Hide modal
    elements.modalKonfirmasiReset.hide();
    
    // Refresh current view
    if (!elements.dashboardContent.classList.contains('d-none')) {
        updateDashboardData();
        renderRingkasanTable();
    } else if (!elements.pegawaiContent.classList.contains('d-none')) {
        renderPegawaiTable();
    } else if (!elements.golonganContent.classList.contains('d-none')) {
        renderGolonganTable();
    } else if (!elements.hariLiburContent.classList.contains('d-none')) {
        renderHariLiburTable();
    } else if (!elements.pengaturanContent.classList.contains('d-none')) {
        renderPengaturanForm();
    }
    
    // Show success message
    showToast('Reset data berhasil');
}

// Fungsi untuk export data ke Excel
function exportToExcel() {
    // Create workbook
    const wb = XLSX.utils.book_new();
    
    // Create worksheet for ringkasan
    const wsRingkasan = createRingkasanWorksheet();
    XLSX.utils.book_append_sheet(wb, wsRingkasan, 'Ringkasan Uang Makan');
    
    // Create worksheet for pegawai
    const wsPegawai = createPegawaiWorksheet();
    XLSX.utils.book_append_sheet(wb, wsPegawai, 'Data Pegawai');
    
    // Create worksheet for golongan
    const wsGolongan = createGolonganWorksheet();
    XLSX.utils.book_append_sheet(wb, wsGolongan, 'Data Golongan');
    
    // Create worksheet for hari libur
    const wsHariLibur = createHariLiburWorksheet();
    XLSX.utils.book_append_sheet(wb, wsHariLibur, 'Data Hari Libur');
    
    // Export workbook
    XLSX.writeFile(wb, `laporan_uang_makan_${new Date().toISOString().split('T')[0]}.xlsx`);
    
    // Show success message
    showToast('Export Excel berhasil');
}

// Fungsi untuk membuat worksheet ringkasan
function createRingkasanWorksheet() {
    // Create header
    const header = ['No', 'Nama Pegawai', 'Golongan', 'Hari Kerja', 'Uang Makan/Hari', 'Total'];
    
    // Create data
    const data = appData.pegawai.map((pegawai, index) => {
        const golongan = appData.golongan.find(g => g.id === pegawai.golonganId);
        if (!golongan) return null;
        
        // Ambil bulan dan tahun yang dipilih
        const selectedMonth = parseInt(elements.selectBulanDashboard.value);
        const selectedYear = parseInt(elements.selectTahunDashboard.value);
        
        const hariKerja = pegawai.hariKerja || hitungHariKerja(selectedMonth, selectedYear, pegawai.id);
        const uangMakanPerHari = golongan.uangMakan;
        const totalUangMakan = hariKerja * uangMakanPerHari;
        
        return [
            index + 1,
            pegawai.nama,
            golongan.nama,
            hariKerja,
            uangMakanPerHari,
            totalUangMakan
        ];
    }).filter(Boolean);
    
    // Add total row
    const totalUangMakan = data.reduce((total, row) => total + row[5], 0);
    data.push([null, null, null, null, 'TOTAL', totalUangMakan]);
    
    // Create worksheet
    const ws = XLSX.utils.aoa_to_sheet([header, ...data]);
    
    // Set column widths
    ws['!cols'] = [
        { wch: 5 },  // No
        { wch: 30 }, // Nama Pegawai
        { wch: 15 }, // Golongan
        { wch: 10 }, // Hari Kerja
        { wch: 15 }, // Uang Makan/Hari
        { wch: 15 }  // Total
    ];
    
    return ws;
}

// Fungsi untuk membuat worksheet pegawai
function createPegawaiWorksheet() {
    // Create header
    const header = ['No', 'Nama Pegawai', 'Golongan', 'Hari Kerja'];
    
    // Create data
    const data = appData.pegawai.map((pegawai, index) => {
        const golongan = appData.golongan.find(g => g.id === pegawai.golonganId);
        if (!golongan) return null;
        
        return [
            index + 1,
            pegawai.nama,
            golongan.nama,
            pegawai.hariKerja || hitungHariKerja(null, null, pegawai.id)
        ];
    }).filter(Boolean);
    
    // Create worksheet
    const ws = XLSX.utils.aoa_to_sheet([header, ...data]);
    
    // Set column widths
    ws['!cols'] = [
        { wch: 5 },  // No
        { wch: 30 }, // Nama Pegawai
        { wch: 15 }, // Golongan
        { wch: 10 }  // Hari Kerja
    ];
    
    return ws;
}

// Fungsi untuk membuat worksheet golongan
function createGolonganWorksheet() {
    // Create header
    const header = ['No', 'Nama Golongan', 'Uang Makan/Hari'];
    
    // Create data
    const data = appData.golongan.map((golongan, index) => {
        return [
            index + 1,
            golongan.nama,
            golongan.uangMakan
        ];
    });
    
    // Create worksheet
    const ws = XLSX.utils.aoa_to_sheet([header, ...data]);
    
    // Set column widths
    ws['!cols'] = [
        { wch: 5 },  // No
        { wch: 20 }, // Nama Golongan
        { wch: 15 }  // Uang Makan/Hari
    ];
    
    return ws;
}

// Fungsi untuk membuat worksheet hari libur
function createHariLiburWorksheet() {
    // Create header
    const header = ['No', 'Tanggal', 'Keterangan'];
    
    // Create data
    const data = appData.hariLibur.map((hariLibur, index) => {
        const tanggal = new Date(hariLibur.tanggal);
        const formattedTanggal = tanggal.toLocaleDateString('id-ID', { day: 'numeric', month: 'long', year: 'numeric' });
        
        return [
            index + 1,
            formattedTanggal,
            hariLibur.keterangan
        ];
    });
    
    // Create worksheet
    const ws = XLSX.utils.aoa_to_sheet([header, ...data]);
    
    // Set column widths
    ws['!cols'] = [
        { wch: 5 },  // No
        { wch: 20 }, // Tanggal
        { wch: 30 }  // Keterangan
    ];
    
    return ws;
}

// Fungsi untuk menghitung hari kerja berdasarkan bulan dan tahun
function hitungHariKerja(bulan = null, tahun = null, pegawaiId = null) {
    // Jika bulan dan tahun tidak disediakan, gunakan bulan dan tahun saat ini
    const today = new Date();
    const year = tahun !== null ? tahun : today.getFullYear();
    const month = bulan !== null ? bulan : today.getMonth();
    
    // Jumlah hari dalam bulan yang dipilih
    const daysInMonth = new Date(year, month + 1, 0).getDate();
    
    let hariKerja = 0;
    
    for (let day = 1; day <= daysInMonth; day++) {
        const date = new Date(year, month, day);
        const dayOfWeek = date.getDay();
        
        // Skip Sabtu (6) dan Minggu (0)
        if (dayOfWeek !== 0 && dayOfWeek !== 6) {
            // Check if it's not a holiday
            const isHoliday = appData.hariLibur.some(hl => {
                const holidayDate = new Date(hl.tanggal);
                return holidayDate.getDate() === day && 
                       holidayDate.getMonth() === month && 
                       holidayDate.getFullYear() === year;
            });
            
            // Check if the employee is absent on this day
            const isAbsent = pegawaiId && appData.ketidakhadiran.some(k => {
                if (k.pegawaiId !== pegawaiId) return false;
                
                const absentDate = new Date(k.tanggal);
                return absentDate.getDate() === day && 
                       absentDate.getMonth() === month && 
                       absentDate.getFullYear() === year;
            });
            
            if (!isHoliday && !isAbsent) {
                hariKerja++;
            }
        }
    }
    
    return hariKerja;
}

// Fungsi untuk menghitung hari kerja bulan ini (untuk kompatibilitas)
function hitungHariKerjaBulanIni() {
    return hitungHariKerja(null, null, null);
}

// Fungsi untuk menghitung total uang makan
function hitungTotalUangMakan(bulan = null, tahun = null) {
    let total = 0;
    
    appData.pegawai.forEach(pegawai => {
        const golongan = appData.golongan.find(g => g.id === pegawai.golonganId);
        if (!golongan) return;
        
        // Jika pegawai memiliki hari kerja kustom, gunakan itu
        // Jika tidak, hitung hari kerja berdasarkan bulan dan tahun yang dipilih, dengan memperhitungkan ketidakhadiran
        const hariKerja = pegawai.hariKerja || hitungHariKerja(bulan, tahun, pegawai.id);
        const uangMakanPerHari = golongan.uangMakan;
        
        total += hariKerja * uangMakanPerHari;
    });
    
    return total;
}

// Fungsi untuk format angka ke format Rupiah
function formatRupiah(angka) {
    return 'Rp ' + angka.toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.');
}

// Fungsi untuk generate ID unik
function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2, 5);
}

// Fungsi untuk menampilkan toast notification
function showToast(message) {
    // Check if toast container exists
    let toastContainer = document.querySelector('.toast-container');
    
    if (!toastContainer) {
        // Create toast container
        toastContainer = document.createElement('div');
        toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
        document.body.appendChild(toastContainer);
    }
    
    // Create toast element
    const toastEl = document.createElement('div');
    toastEl.className = 'toast align-items-center text-white bg-success border-0';
    toastEl.setAttribute('role', 'alert');
    toastEl.setAttribute('aria-live', 'assertive');
    toastEl.setAttribute('aria-atomic', 'true');
    
    // Create toast content
    toastEl.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    `;
    
    // Add toast to container
    toastContainer.appendChild(toastEl);
    
    // Initialize toast
    const toast = new bootstrap.Toast(toastEl, { delay: 3000 });
    
    // Show toast
    toast.show();
    
    // Remove toast after it's hidden
    toastEl.addEventListener('hidden.bs.toast', () => {
        toastEl.remove();
    });
}

// Fungsi untuk toggle sidebar pada perangkat mobile
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    sidebar.classList.toggle('show');
}

// Fungsi untuk menutup sidebar setelah navigasi pada perangkat mobile
function toggleSidebarOnMobile() {
    if (window.innerWidth <= 768) {
        const sidebar = document.getElementById('sidebar');
        sidebar.classList.remove('show');
    }
}