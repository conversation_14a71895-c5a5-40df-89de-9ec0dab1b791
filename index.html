<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Aplikasi Penghitungan Uang Makan Pegawai</title>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <!-- Tombol Toggle Sidebar untuk Mobile -->
    <button class="sidebar-toggle" id="sidebar-toggle">
        <i class="bi bi-list"></i>
    </button>
    
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav id="sidebar" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h5 class="fw-bold">Aplikasi Uang Makan</h5>
                    </div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="#" id="nav-dashboard">
                                <i class="bi bi-house-door me-2"></i>
                                Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" id="nav-pegawai">
                                <i class="bi bi-people me-2"></i>
                                Data Pegawai
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" id="nav-golongan">
                                <i class="bi bi-diagram-3 me-2"></i>
                                Golongan
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" id="nav-hari-libur">
                                <i class="bi bi-calendar-event me-2"></i>
                                Hari Libur
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" id="nav-ketidakhadiran">
                                <i class="bi bi-person-x me-2"></i>
                                Ketidakhadiran
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" id="nav-pengaturan">
                                <i class="bi bi-gear me-2"></i>
                                Pengaturan
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main Content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2" id="page-title">Dashboard</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button type="button" class="btn btn-sm btn-outline-primary me-2" id="btn-export-excel">
                            <i class="bi bi-file-earmark-excel me-1"></i>
                            Export Excel
                        </button>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="dropdownMenuButton" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-database me-1"></i>
                                Database
                            </button>
                            <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                <li><a class="dropdown-item" href="#" id="btn-backup">Backup Data</a></li>
                                <li><a class="dropdown-item" href="#" id="btn-restore">Restore Data</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-danger" href="#" id="btn-reset">Reset Data</a></li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Dashboard Content -->
                <div id="dashboard-content" class="content-section">
                    <div class="row row-cols-1 row-cols-md-2 row-cols-lg-4 g-4 mb-4">
                        <div class="col">
                            <div class="card h-100 border-0 shadow-sm">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="card-subtitle mb-1 text-muted">Total Pegawai</h6>
                                            <h2 class="card-title mb-0" id="total-pegawai">0</h2>
                                        </div>
                                        <div class="bg-primary bg-opacity-10 p-3 rounded-circle">
                                            <i class="bi bi-people text-primary fs-4"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col">
                            <div class="card h-100 border-0 shadow-sm">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="card-subtitle mb-1 text-muted">Total Golongan</h6>
                                            <h2 class="card-title mb-0" id="total-golongan">0</h2>
                                        </div>
                                        <div class="bg-success bg-opacity-10 p-3 rounded-circle">
                                            <i class="bi bi-diagram-3 text-success fs-4"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col">
                            <div class="card h-100 border-0 shadow-sm">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="card-subtitle mb-1 text-muted">Hari Kerja</h6>
                                            <h2 class="card-title mb-0" id="total-hari-kerja">0</h2>
                                        </div>
                                        <div class="bg-info bg-opacity-10 p-3 rounded-circle">
                                            <i class="bi bi-calendar-check text-info fs-4"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col">
                            <div class="card h-100 border-0 shadow-sm">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="card-subtitle mb-1 text-muted">Total Uang Makan</h6>
                                            <h2 class="card-title mb-0" id="total-uang-makan">Rp 0</h2>
                                        </div>
                                        <div class="bg-warning bg-opacity-10 p-3 rounded-circle">
                                            <i class="bi bi-cash-stack text-warning fs-4"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card border-0 shadow-sm mb-4">
                        <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">Ringkasan Uang Makan</h5>
                            <div class="d-flex">
                                <select class="form-select form-select-sm me-2" id="select-bulan-dashboard">
                                    <option value="0">Januari</option>
                                    <option value="1">Februari</option>
                                    <option value="2">Maret</option>
                                    <option value="3">April</option>
                                    <option value="4">Mei</option>
                                    <option value="5">Juni</option>
                                    <option value="6">Juli</option>
                                    <option value="7">Agustus</option>
                                    <option value="8">September</option>
                                    <option value="9">Oktober</option>
                                    <option value="10">November</option>
                                    <option value="11">Desember</option>
                                </select>
                                <input type="number" class="form-control form-control-sm" id="select-tahun-dashboard" placeholder="Tahun" min="2000" max="2100">
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover" id="table-ringkasan">
                                    <thead>
                                        <tr>
                                            <th scope="col">No</th>
                                            <th scope="col">Nama Pegawai</th>
                                            <th scope="col">Golongan</th>
                                            <th scope="col">Hari Kerja</th>
                                            <th scope="col">Uang Makan/Hari</th>
                                            <th scope="col">Total</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- Data akan diisi oleh JavaScript -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Pegawai Content -->
                <div id="pegawai-content" class="content-section d-none">
                    <div class="card border-0 shadow-sm mb-4">
                        <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">Data Pegawai</h5>
                            <button class="btn btn-primary" id="btn-tambah-pegawai">
                                <i class="bi bi-plus-circle me-1"></i> Tambah Pegawai
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover" id="table-pegawai">
                                    <thead>
                                        <tr>
                                            <th scope="col">No</th>
                                            <th scope="col">Nama Pegawai</th>
                                            <th scope="col">Golongan</th>
                                            <th scope="col">Hari Kerja</th>
                                            <th scope="col">Aksi</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- Data akan diisi oleh JavaScript -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Golongan Content -->
                <div id="golongan-content" class="content-section d-none">
                    <div class="card border-0 shadow-sm mb-4">
                        <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">Data Golongan</h5>
                            <button class="btn btn-primary" id="btn-tambah-golongan">
                                <i class="bi bi-plus-circle me-1"></i> Tambah Golongan
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover" id="table-golongan">
                                    <thead>
                                        <tr>
                                            <th scope="col">No</th>
                                            <th scope="col">Nama Golongan</th>
                                            <th scope="col">Uang Makan/Hari</th>
                                            <th scope="col">Aksi</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- Data akan diisi oleh JavaScript -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Hari Libur Content -->
                <div id="hari-libur-content" class="content-section d-none">
                    <div class="card border-0 shadow-sm mb-4">
                        <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">Data Hari Libur</h5>
                            <button class="btn btn-primary" id="btn-tambah-hari-libur">
                                <i class="bi bi-plus-circle me-1"></i> Tambah Hari Libur
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <div class="input-group">
                                        <span class="input-group-text">Bulan</span>
                                        <select class="form-select" id="select-bulan-hari-libur">
                                            <option value="0">Januari</option>
                                            <option value="1">Februari</option>
                                            <option value="2">Maret</option>
                                            <option value="3">April</option>
                                            <option value="4">Mei</option>
                                            <option value="5">Juni</option>
                                            <option value="6">Juli</option>
                                            <option value="7">Agustus</option>
                                            <option value="8">September</option>
                                            <option value="9">Oktober</option>
                                            <option value="10">November</option>
                                            <option value="11">Desember</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="input-group">
                                        <span class="input-group-text">Tahun</span>
                                        <select class="form-select" id="select-tahun-hari-libur">
                                            <!-- Akan diisi oleh JavaScript -->
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-hover" id="table-hari-libur">
                                    <thead>
                                        <tr>
                                            <th scope="col">No</th>
                                            <th scope="col">Tanggal</th>
                                            <th scope="col">Keterangan</th>
                                            <th scope="col">Aksi</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- Data akan diisi oleh JavaScript -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Ketidakhadiran Content -->
                <div id="ketidakhadiran-content" class="content-section d-none">
                    <div class="card border-0 shadow-sm mb-4">
                        <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">Data Ketidakhadiran</h5>
                            <div class="d-flex">
                                <select class="form-select form-select-sm me-2" id="select-bulan-ketidakhadiran">
                                    <option value="0">Januari</option>
                                    <option value="1">Februari</option>
                                    <option value="2">Maret</option>
                                    <option value="3">April</option>
                                    <option value="4">Mei</option>
                                    <option value="5">Juni</option>
                                    <option value="6">Juli</option>
                                    <option value="7">Agustus</option>
                                    <option value="8">September</option>
                                    <option value="9">Oktober</option>
                                    <option value="10">November</option>
                                    <option value="11">Desember</option>
                                </select>
                                <input type="number" class="form-control form-control-sm me-2" id="select-tahun-ketidakhadiran" placeholder="Tahun" min="2000" max="2100">
                                <button class="btn btn-primary btn-sm" id="btn-tambah-ketidakhadiran">
                                    <i class="bi bi-plus-circle me-1"></i> Tambah
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover" id="table-ketidakhadiran">
                                    <thead>
                                        <tr>
                                            <th scope="col">No</th>
                                            <th scope="col">Nama Pegawai</th>
                                            <th scope="col">Tanggal</th>
                                            <th scope="col">Keterangan</th>
                                            <th scope="col">Aksi</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- Data akan diisi oleh JavaScript -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Pengaturan Content -->
                <div id="pengaturan-content" class="content-section d-none">
                    <div class="card border-0 shadow-sm mb-4">
                        <div class="card-header bg-white py-3">
                            <h5 class="card-title mb-0">Pengaturan Aplikasi</h5>
                        </div>
                        <div class="card-body">
                            <form id="form-pengaturan">
                                <div class="mb-3">
                                    <label for="input-nama-instansi" class="form-label">Nama Instansi</label>
                                    <input type="text" class="form-control" id="input-nama-instansi" placeholder="Masukkan nama instansi">
                                </div>
                                <div class="mb-3">
                                    <label for="input-alamat-instansi" class="form-label">Alamat Instansi</label>
                                    <textarea class="form-control" id="input-alamat-instansi" rows="3" placeholder="Masukkan alamat instansi"></textarea>
                                </div>
                                <div class="mb-3">
                                    <label for="input-nama-pimpinan" class="form-label">Nama Pimpinan</label>
                                    <input type="text" class="form-control" id="input-nama-pimpinan" placeholder="Masukkan nama pimpinan">
                                </div>
                                <div class="mb-3">
                                    <label for="input-nip-pimpinan" class="form-label">NIP Pimpinan</label>
                                    <input type="text" class="form-control" id="input-nip-pimpinan" placeholder="Masukkan NIP pimpinan">
                                </div>
                                <button type="submit" class="btn btn-primary">Simpan Pengaturan</button>
                            </form>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Modals -->
    <!-- Modal Tambah/Edit Pegawai -->
    <div class="modal fade" id="modal-pegawai" tabindex="-1" aria-labelledby="modal-pegawai-label" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modal-pegawai-label">Tambah Pegawai</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="form-pegawai">
                        <input type="hidden" id="input-id-pegawai">
                        <div class="mb-3">
                            <label for="input-nama-pegawai" class="form-label">Nama Pegawai</label>
                            <input type="text" class="form-control" id="input-nama-pegawai" required>
                        </div>
                        <div class="mb-3">
                            <label for="select-golongan-pegawai" class="form-label">Golongan</label>
                            <select class="form-select" id="select-golongan-pegawai" required>
                                <!-- Akan diisi oleh JavaScript -->
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="input-hari-kerja-pegawai" class="form-label">Jumlah Hari Kerja</label>
                            <input type="number" class="form-control" id="input-hari-kerja-pegawai" min="0" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="button" class="btn btn-primary" id="btn-simpan-pegawai">Simpan</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Tambah/Edit Golongan -->
    <div class="modal fade" id="modal-golongan" tabindex="-1" aria-labelledby="modal-golongan-label" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modal-golongan-label">Tambah Golongan</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="form-golongan">
                        <input type="hidden" id="input-id-golongan">
                        <div class="mb-3">
                            <label for="input-nama-golongan" class="form-label">Nama Golongan</label>
                            <input type="text" class="form-control" id="input-nama-golongan" required>
                        </div>
                        <div class="mb-3">
                            <label for="input-uang-makan-golongan" class="form-label">Uang Makan per Hari</label>
                            <div class="input-group">
                                <span class="input-group-text">Rp</span>
                                <input type="number" class="form-control" id="input-uang-makan-golongan" min="0" required>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="button" class="btn btn-primary" id="btn-simpan-golongan">Simpan</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Tambah/Edit Hari Libur -->
    <div class="modal fade" id="modal-hari-libur" tabindex="-1" aria-labelledby="modal-hari-libur-label" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modal-hari-libur-label">Tambah Hari Libur</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="form-hari-libur">
                        <input type="hidden" id="input-id-hari-libur">
                        <div class="mb-3">
                            <label for="input-tanggal-hari-libur" class="form-label">Tanggal</label>
                            <input type="date" class="form-control" id="input-tanggal-hari-libur" required>
                        </div>
                        <div class="mb-3">
                            <label for="input-keterangan-hari-libur" class="form-label">Keterangan</label>
                            <input type="text" class="form-control" id="input-keterangan-hari-libur" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="button" class="btn btn-primary" id="btn-simpan-hari-libur">Simpan</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Konfirmasi Reset -->
    <div class="modal fade" id="modal-konfirmasi-reset" tabindex="-1" aria-labelledby="modal-konfirmasi-reset-label" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modal-konfirmasi-reset-label">Konfirmasi Reset Data</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Apakah Anda yakin ingin mereset semua data? Tindakan ini tidak dapat dibatalkan.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="button" class="btn btn-danger" id="btn-konfirmasi-reset">Reset Data</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Input file untuk restore -->
    <input type="file" id="input-restore-file" accept=".json" style="display: none;">

    <!-- Modal Tambah/Edit Ketidakhadiran -->
    <div class="modal fade" id="modal-ketidakhadiran" tabindex="-1" aria-labelledby="modal-ketidakhadiran-label" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modal-ketidakhadiran-label">Tambah Ketidakhadiran</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="form-ketidakhadiran">
                        <input type="hidden" id="input-id-ketidakhadiran">
                        <div class="mb-3">
                            <label for="select-pegawai-ketidakhadiran" class="form-label">Pegawai</label>
                            <select class="form-select" id="select-pegawai-ketidakhadiran" required>
                                <!-- Akan diisi oleh JavaScript -->
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="input-tanggal-ketidakhadiran" class="form-label">Tanggal</label>
                            <input type="date" class="form-control" id="input-tanggal-ketidakhadiran" required>
                        </div>
                        <div class="mb-3">
                            <label for="input-keterangan-ketidakhadiran" class="form-label">Keterangan</label>
                            <input type="text" class="form-control" id="input-keterangan-ketidakhadiran" placeholder="Sakit, Izin, dll" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="button" class="btn btn-primary" id="btn-simpan-ketidakhadiran">Simpan</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <!-- SheetJS (xlsx) for Excel export -->
    <script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
    <!-- Custom JS -->
    <script src="js/app.js"></script>
</body>
</html>