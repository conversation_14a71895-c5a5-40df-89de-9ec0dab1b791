/* Custom CSS for Aplikasi Penghitungan Uang Ma<PERSON> */

:root {
    --primary-color: #4e73df;
    --secondary-color: #858796;
    --success-color: #1cc88a;
    --info-color: #36b9cc;
    --warning-color: #f6c23e;
    --danger-color: #e74a3b;
    --light-color: #f8f9fc;
    --dark-color: #5a5c69;
}

body {
    font-family: 'Nunito', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background-color: #f8f9fc;
    color: #5a5c69;
}

/* Sidebar Styles */
#sidebar {
    min-height: 100vh;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    z-index: 1;
    transition: all 0.3s;
}

#sidebar .nav-link {
    color: #5a5c69;
    padding: 0.75rem 1rem;
    border-radius: 0.35rem;
    margin: 0.2rem 0;
    transition: all 0.3s;
}

#sidebar .nav-link:hover {
    background-color: rgba(78, 115, 223, 0.1);
    color: var(--primary-color);
}

#sidebar .nav-link.active {
    background-color: rgba(78, 115, 223, 0.2);
    color: var(--primary-color);
    font-weight: 600;
}

/* Card Styles */
.card {
    border-radius: 0.5rem;
    transition: all 0.3s;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-header {
    border-top-left-radius: 0.5rem !important;
    border-top-right-radius: 0.5rem !important;
}

/* Table Styles */
.table th {
    font-weight: 600;
    background-color: #f8f9fc;
}

.table-hover tbody tr:hover {
    background-color: rgba(78, 115, 223, 0.05);
}

/* Button Styles */
.btn {
    border-radius: 0.35rem;
    padding: 0.375rem 0.75rem;
    font-weight: 500;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #2e59d9;
    border-color: #2653d4;
}

/* Form Styles */
.form-control, .form-select {
    border-radius: 0.35rem;
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d3e2;
}

.form-control:focus, .form-select:focus {
    border-color: #bac8f3;
    box-shadow: 0 0 0 0.25rem rgba(78, 115, 223, 0.25);
}

/* Modal Styles */
.modal-content {
    border-radius: 0.5rem;
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.modal-header {
    border-top-left-radius: 0.5rem;
    border-top-right-radius: 0.5rem;
    background-color: #f8f9fc;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    #sidebar {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 1030;
        overflow-y: auto;
        transform: translateX(-100%);
    }
    
    #sidebar.show {
        transform: translateX(0);
    }
    
    .navbar-toggler {
        display: block;
    }
    
    main {
        margin-left: 0 !important;
        width: 100% !important;
    }
}

/* Utility Classes */
.shadow-sm {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

.rounded-circle {
    border-radius: 50% !important;
}

/* Animation */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Custom Toggle Button for Mobile */
.sidebar-toggle {
    position: fixed;
    bottom: 1rem;
    right: 1rem;
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    z-index: 1031;
    display: none;
}

@media (max-width: 768px) {
    .sidebar-toggle {
        display: flex;
    }
}